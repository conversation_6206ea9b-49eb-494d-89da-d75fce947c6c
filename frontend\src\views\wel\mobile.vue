<template>
  <div class="banner-wrapper">
    <Banner v-show="!currentComponent" />
  </div>

  <div class="mobile-container">
    <!-- 页面头部 -->

    <AgendaCard v-show="!currentComponent" />

    <header class="page-header" v-show="currentComponent">
      <div class="header-placeholder"></div>
    </header>

    <!-- 功能模块区域 -->
    <section class="function-grid" v-show="!currentComponent">
      <!-- 第一排 -->
      <!-- <GlowingCard
        icon-class="fas fa-calendar-alt"
        title="会议议程"
        subtitle="AGENDA"
        color="#ff00dd"
        @navigate="showComponent('agenda')"
      /> -->
      <GlowingCard
        icon-class="fas fa-broadcast-tower"
        title="云直播"
        subtitle="LIVE STREAM"
        color="#00ffe1"
        @navigate="showComponent('live-stream')"
      />

      <!-- 第二排 -->
      <GlowingCard
        icon-class="fas fa-video"
        title="分会场信息"
        subtitle="SUB VENUES"
        color="white"
        @navigate="showComponent('sub-venues')"
      />
      <GlowingCard
        icon-class="fas fa-file-alt"
        title="会议资料"
        subtitle="MATERIALS"
        color="white"
        @navigate="showComponent('materials')"
      />

      <!-- 第三排 -->
      <GlowingCard
        icon-class="fas fa-images"
        title="在线相册"
        subtitle="PHOTO"
        color="#00ffe1"
        @navigate="showComponent('photo')"
      />
      <GlowingCard
        icon-class="fas fa-book-open"
        title="参会指南"
        subtitle="GUIDE"
        color="#00ffe1"
        @navigate="showComponent('guide')"
      />

      <!-- 第四排 -->
      <GlowingCard
        icon-class="fas fa-robot"
        title="会务助手"
        subtitle="AI ASSISTANT"
        color="white"
        @navigate="showComponent('ai-chat')"
      />
      <!-- <GlowingCard
        icon-class="fas fa-user"
        title="个人中心"
        subtitle="PROFILE"
        color="#66ff66"
        @navigate="showComponent('profile')"
      /> -->
    </section>

    <!-- 动态组件显示区域 -->
    <div class="component-container" v-show="currentComponent">
      <component :is="currentComponentName" @navigate="handleNavigate" />
    </div>

    <BottomNav
      :navItems="navItems"
      :activeIndex="activeNavIndex"
      @navigate="showComponent"
    />
  </div>
</template>

<script>
// 导入所有需要的组件
import Agenda from "./Agenda.vue";
import LiveStream from "./LiveStream.vue";
import SubVenues from "./SubVenues.vue";
import Materials from "./Materials.vue";
import Photo from "./Photo.vue";
import Guide from "./Guide.vue";
import AiChat from "./AiChat.vue";
import Profile from "./Profile.vue";
import MyDining from "./MyDining.vue";
import MyAccommodation from "./MyAccommodation.vue";
import MySchedule from "./MySchedule.vue";
import CheckIn from "./CheckIn.vue";
import { getDictionary } from "@/api/system/dictbiz";
import Banner from "../util/banner.vue";
import GlowingCard from "../util/glowing-card.vue";
import BottomNav from "@/components/bottom-nav/main.vue";
import SlideCard from "@/components/slide-card/main.vue";
import AgendaCard from "@/components/agenda-card/main.vue";
import { mapGetters } from 'vuex'

export default {
  name: "MobilePage",
  components: {
    Agenda,
    LiveStream,
    SubVenues,
    Materials,
    Photo,
    Guide,
    AiChat,
    Profile,
    MyDining,
    MyAccommodation,
    MySchedule,
    CheckIn,
    Banner,
    GlowingCard,
    BottomNav,
    SlideCard,
    AgendaCard,
  },
  data() {
    return {
      currentComponent: null, // 当前显示的组件名称
      activeNavIndex: 0, // 当前激活的导航索引
      previousComponent: null, // 上一个组件，用于返回导航
      mainTitle: "", // 主标题，从字典获取
      subTitle: "",
      date: "",
      organizer: "",
      navItems: [
        { label: "主页", iconClass: "fa-house-chimney", routeName: null },
        { label: "签到", iconClass: "fa-location-dot", routeName: "checkin" },
        { label: "我的", iconClass: "fa-user-tie", routeName: "profile" },
      ],
    };
  },
  async mounted() {
    // 检查用户登录状态
    await this.checkUserAuth()
    // 由于路由守卫已经处理了登录检查，这里只需要加载数据
    await this.loadDictionary();
    this.loadFontAwesome();
    this.currentComponent = null;
    this.activeNavIndex = 0;
  },
  computed: {
    ...mapGetters(['token', 'userInfo', 'isMobileLoggedIn']),
    currentComponentName() {
      if (!this.currentComponent) return null;

      // 将组件名称映射到实际的组件名
      const componentMap = {
        agenda: "Agenda",
        "live-stream": "LiveStream",
        "sub-venues": "SubVenues",
        materials: "Materials",
        photo: "Photo",
        guide: "Guide",
        "ai-chat": "AiChat",
        profile: "Profile",
        "my-dining": "MyDining",
        "my-accommodation": "MyAccommodation",
        "my-schedule": "MySchedule",
        checkin: "CheckIn",
      };

      return componentMap[this.currentComponent] || null;
    },
  },
  methods: {
    // 钉钉扫一扫
    async openDingScan() {
      try {
        if (typeof window.dd === 'undefined' || !window.dd?.biz?.util?.scan) {
          console.warn('钉钉 JSAPI 不可用，请确认在钉钉内打开且已完成签名初始化');
          this.$message && this.$message.warning
            ? this.$message.warning('请在钉钉内打开，或确认已完成钉钉JSAPI初始化')
            : alert('请在钉钉内打开，或确认已完成钉钉JSAPI初始化');
          return;
        }
        window.dd.biz.util.scan({
          type: 'qr',
          onSuccess: (res) => {
            console.log('扫码成功:', res);
            const text = res?.text || '';
            this.$message && this.$message.success
              ? this.$message.success('扫码成功')
              : alert('扫码成功：' + text);
            // 示例：根据业务处理扫码结果
            // this.$router.push({ name: 'Checkin', query: { code: text } })
          },
          onFail: (err) => {
            console.error('扫码失败:', err);
            this.$message && this.$message.error
              ? this.$message.error('扫码失败')
              : alert('扫码失败');
          }
        });
      } catch (e) {
        console.error('调用钉钉扫码异常:', e);
        this.$message && this.$message.error
          ? this.$message.error('调用钉钉扫码异常')
          : alert('调用钉钉扫码异常');
      }
    },
    // 检查移动端登录状态（已移至路由守卫处理）
    // 保留此方法以备将来需要组件级别的登录状态检查
    async checkUserAuth() {
      // 路由守卫已经确保用户已登录才能访问此页面
      // 这里只做日志记录和状态确认

      console.log('当前UserInfo:', this.userInfo);

      // 使用getter检查移动端登录状态（同步方法）
      const isLoggedIn = this.isMobileLoggedIn;
      console.log('isMobileLoggedIn getter 返回结果:', isLoggedIn);

      if (!isLoggedIn) {
        console.log('用户未登录，跳转到钉钉登录')

        // 保存当前路径到localStorage，登录成功后返回
        localStorage.setItem('redirectPath', this.$route.fullPath);
        // 跳转到钉钉登录页面
        this.$router.push('/dinglogin');
        return
      }

      console.log('用户信息:', this.userInfo)
      console.log('用户已通过路由守卫验证，可以正常使用mobile功能');
    },
    loadFontAwesome() {
      // 动态引入Font Awesome（与首页保持一致）
      if (!document.getElementById("fa-mobile-page")) {
        const link = document.createElement("link");
        link.id = "fa-mobile-page";
        link.rel = "stylesheet";
        link.href =
          "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
        document.head.appendChild(link);
      }
    },
    async loadDictionary() {
      try {
        const response = await getDictionary("wel_config");
        if (response && response.length > 0) {
          response.forEach((item) => {
            if (item.dictKey === "main_title") {
              this.mainTitle = item.dictValue;
            } else if (item.dictKey === "sub_title") {
              this.subTitle = item.dictValue;
            } else if (item.dictKey === "date") {
              this.date = item.dictValue;
            } else if (item.dictKey === "organizer") {
              this.organizer = item.dictValue;
            }
          });
        }
      } catch (error) {
        console.error("加载字典数据失败:", error);
      }
    },
    showComponent(componentName) {
      this.currentComponent = componentName;

      const index = this.navItems.findIndex(
        (item) => item.routeName === componentName
      );
      // 仅当匹配 navItem 时更新 activeIndex，否则设为 null
      this.activeNavIndex = index >= 0 ? index : null;
    },
    goBack() {
      // 如果有上一个组件，返回到上一个组件，否则返回首页
      if (this.previousComponent) {
        this.currentComponent = this.previousComponent;
        this.previousComponent = null;
      } else {
        this.currentComponent = null;
        console.log("返回首页");
      }
    },
    handleNavigate(componentName) {
      this.showComponent(componentName);
      // 如果目标组件不在底部导航 navItems 中，则不修改 activeIndex
      const index = this.navItems.findIndex(
        (item) => item.routeName === componentName
      );
      this.activeNavIndex = index >= 0 ? index : null;
    },
    getComponentTitle() {
      const titleMap = {
        agenda: "会议议程",
        "live-stream": "云直播",
        "sub-venues": "分会场信息",
        materials: "会议资料",
        photo: "在线相册",
        guide: "参会指南",
        "ai-chat": "会务助手",
        profile: "个人中心",
        "my-dining": "我的用餐",
        "my-accommodation": "我的住宿",
        "my-schedule": "我的日程",
      };
      return titleMap[this.currentComponent] || "";
    },
  },
};
</script>

<style scoped>
/* 移动端专用样式 - 基于首页样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.banner-wrapper {
  width: 100vw;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 10;
}
.banner-wrapper::after {
  clear: both;
  content: "";
  display: block;
}

/* 移动端容器 */
.mobile-container {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  min-height: 100vh;
  background: url("img/bg08.jpg") no-repeat center center fixed;
  background-size: cover;
  max-width: 480px;
  margin: 0 auto;
  padding: 1rem;
  position: relative;
}

.title-section {
  color: white;
  text-align: center;
  margin-top: 5%;
  margin-bottom: 20%;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

.main-title {
  font-size: 30px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
  background: linear-gradient(90deg, #00d5ff 0%, #ec46ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.conference-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.main-title,
.conference-title {
  font-size: 22px;
  font-weight: bold;
  color: #ffffff;
}

.conference-title,
.organizer,
.date {
  color: #ffffff;
  opacity: 0.85;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.page-header h1 {
  color: white;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.header-placeholder {
  width: 40px;
}

/* 功能网格 */
.function-grid {
  display: flex;
  flex-wrap: wrap; /* 允许子元素换行 */
  gap: 15px;
}
.function-grid > * {
  flex: 0 0 calc(50% - 7.5px); /* 每个卡片占50%宽度，减去一半间距 */
}

.function-card {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(12px);
  border-radius: 18px;
  padding: 20px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  flex: 1;
  z-index: 0;
  color: #fff;
}

.function-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.function-card:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.function-card:hover {
  color: #ec46ff;
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.function-card:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

.card-icon {
  margin-bottom: 8px;
}

.card-icon i {
  font-size: 24px;
  color: #00d5ff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}

/* FontAwesome 图标备用方案 */
.card-icon i.fas.fa-calendar-alt::before {
  content: "📅";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-broadcast-tower::before {
  content: "📡";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-video::before {
  content: "📹";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-file-alt::before {
  content: "📄";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-images::before {
  content: "🖼️";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-book-open::before {
  content: "📖";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-robot::before {
  content: "🤖";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-icon i.fas.fa-user::before {
  content: "👤";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}
.card-icon i.fas.fa-qrcode::before {
    content: "🔍";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-arrow-left::before {
  content: "←";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji",
    sans-serif;
}

.card-text {
  text-align: center;
}

.card-text h3 {
  font-weight: 600;
  margin-bottom: 2px;
  color: #ffffff;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-text p {
  color: #ccccff;
  background: none !important;
  -webkit-background-clip: initial !important;
  -webkit-text-fill-color: initial !important;
  font-size: 11px;
  font-weight: 400;
  letter-spacing: 0.8px;
}

/* 组件容器 */
.component-container {
  width: 100%;
  min-height: calc(100vh - 80px);
}
</style>
